/*!
 * FilePondPluginImageEdit 1.6.3
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */

/* eslint-disable */

const e=e=>/^image/.test(e.type),t=t=>{const{addFilter:i,utils:o,views:n}=t,{Type:r,createRoute:l,createItemAPI:a=(e=>e)}=o,{fileActionButton:d}=n;i("SHOULD_REMOVE_ON_REVERT",(t,{item:i,query:o})=>new Promise(t=>{const{file:n}=i;t(!(o("GET_ALLOW_IMAGE_EDIT")&&o("GET_IMAGE_EDIT_ALLOW_EDIT")&&e(n)))})),i("DID_LOAD_ITEM",(t,{query:i,dispatch:o})=>new Promise((n,r)=>{if(t.origin>1)return void n(t);const{file:l}=t;if(!i("GET_ALLOW_IMAGE_EDIT")||!i("GET_IMAGE_EDIT_INSTANT_EDIT"))return void n(t);if(!e(l))return void n(t);const a=(e,t,i)=>n=>{E.shift(),n?t(e):i(e),o("KICK"),d()},d=()=>{if(!E.length)return;const{item:e,resolve:t,reject:i}=E[0];o("EDIT_ITEM",{id:e.id,handleEditorResponse:a(e,t,i)})};c({item:t,resolve:n,reject:r}),1===E.length&&d()})),i("DID_CREATE_ITEM",(e,{query:t,dispatch:i})=>{e.extend("edit",()=>{i("EDIT_ITEM",{id:e.id})})});const E=[],c=e=>(E.push(e),e);return i("CREATE_VIEW",t=>{const{is:i,view:o,query:n}=t;if(!n("GET_ALLOW_IMAGE_EDIT"))return;const r=n("GET_ALLOW_IMAGE_PREVIEW");if(!(i("file-info")&&!r||i("file")&&r))return;const E=n("GET_IMAGE_EDIT_EDITOR");if(!E)return;E.filepondCallbackBridge||(E.outputData=!0,E.outputFile=!1,E.filepondCallbackBridge={onconfirm:E.onconfirm||(()=>{}),oncancel:E.oncancel||(()=>{})});o.registerDestroyer(({root:e})=>{e.ref.buttonEditItem&&e.ref.buttonEditItem.off("click",e.ref.handleEdit),e.ref.editButton&&e.ref.editButton.removeEventListener("click",e.ref.handleEdit)});const c={EDIT_ITEM:({root:e,props:t,action:i})=>{const{id:o}=t,{handleEditorResponse:n}=i;E.cropAspectRatio=e.query("GET_IMAGE_CROP_ASPECT_RATIO")||E.cropAspectRatio,E.outputCanvasBackgroundColor=e.query("GET_IMAGE_TRANSFORM_CANVAS_BACKGROUND_COLOR")||E.outputCanvasBackgroundColor;const r=e.query("GET_ITEM",o);if(!r)return;const l=r.file,d=r.getMetadata("crop"),c=r.getMetadata("resize"),s=r.getMetadata("filter")||null,I=r.getMetadata("filters")||null,_=r.getMetadata("colors")||null,u=r.getMetadata("markup")||null,T={crop:d||{center:{x:.5,y:.5},flip:{horizontal:!1,vertical:!1},zoom:1,rotation:0,aspectRatio:null},size:c?{upscale:c.upscale,mode:c.mode,width:c.size.width,height:c.size.height}:null,filter:I?I.id||I.matrix:e.query("GET_ALLOW_IMAGE_FILTER")&&e.query("GET_IMAGE_FILTER_COLOR_MATRIX")&&!_?s:null,color:_,markup:u};E.onconfirm=(({data:e})=>{const{crop:t,size:i,filter:o,color:l,colorMatrix:d,markup:c}=e,s={};if(t&&(s.crop=t),i){const e=(r.getMetadata("resize")||{}).size,t={width:i.width,height:i.height};t.width&&t.height||!e||(t.width=e.width,t.height=e.height),(t.width||t.height)&&(s.resize={upscale:i.upscale,mode:i.mode,size:t})}c&&(s.markup=c),s.colors=l,s.filters=o,s.filter=d,r.setMetadata(s),E.filepondCallbackBridge.onconfirm(e,a(r)),n&&(E.onclose=(()=>{n(!0),E.onclose=null}))}),E.oncancel=(()=>{E.filepondCallbackBridge.oncancel(a(r)),n&&(E.onclose=(()=>{n(!1),E.onclose=null}))}),E.open(l,T)},DID_LOAD_ITEM:({root:t,props:i})=>{if(!n("GET_IMAGE_EDIT_ALLOW_EDIT"))return;const{id:l}=i,a=n("GET_ITEM",l);if(!a)return;const E=a.file;if(e(E))if(t.ref.handleEdit=(e=>{e.stopPropagation(),t.dispatch("EDIT_ITEM",{id:l})}),r){const e=o.createChildView(d,{label:"edit",icon:n("GET_IMAGE_EDIT_ICON_EDIT"),opacity:0});e.element.classList.add("filepond--action-edit-item"),e.element.dataset.align=n("GET_STYLE_IMAGE_EDIT_BUTTON_EDIT_ITEM_POSITION"),e.on("click",t.ref.handleEdit),t.ref.buttonEditItem=o.appendChildView(e)}else{const e=o.element.querySelector(".filepond--file-info-main"),i=document.createElement("button");i.className="filepond--action-edit-item-alt",i.innerHTML=n("GET_IMAGE_EDIT_ICON_EDIT")+"<span>edit</span>",i.addEventListener("click",t.ref.handleEdit),e.appendChild(i),t.ref.editButton=i}}};if(r){const e=({root:e})=>{e.ref.buttonEditItem&&(e.ref.buttonEditItem.opacity=1)};c.DID_IMAGE_PREVIEW_SHOW=e}o.registerWriter(l(c))}),{options:{allowImageEdit:[!0,r.BOOLEAN],styleImageEditButtonEditItemPosition:["bottom center",r.STRING],imageEditInstantEdit:[!1,r.BOOLEAN],imageEditAllowEdit:[!0,r.BOOLEAN],imageEditIconEdit:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false"><path d="M8.5 17h1.586l7-7L15.5 8.414l-7 7V17zm-1.707-2.707l8-8a1 1 0 0 1 1.414 0l3 3a1 1 0 0 1 0 1.414l-8 8A1 1 0 0 1 10.5 19h-3a1 1 0 0 1-1-1v-3a1 1 0 0 1 .293-.707z" fill="currentColor" fill-rule="nonzero"/></svg>',r.STRING],imageEditEditor:[null,r.OBJECT]}}};"undefined"!=typeof window&&void 0!==window.document&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:t}));export default t;
