/*!
 * FilePondPluginImagePreview 4.6.11
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */

/* eslint-disable */

const e=(e,t)=>r(e.x*t,e.y*t),t=(e,t)=>r(e.x+t.x,e.y+t.y),i=(e,t,i)=>{const a=Math.cos(t),o=Math.sin(t),n=r(e.x-i.x,e.y-i.y);return r(i.x+a*n.x-o*n.y,i.y+o*n.x+a*n.y)},r=(e=0,t=0)=>({x:e,y:t}),a=(e,t,i=1,r)=>"string"==typeof e?parseFloat(e)*i:"number"==typeof e?e*(r?t[r]:Math.min(t.width,t.height)):void 0,o=e=>null!=e,n=e=>e.map((e,t)=>`${0===t?"M":"L"} ${e.x} ${e.y}`).join(" "),s=(e,t)=>Object.keys(t).forEach(i=>e.setAttribute(i,t[i])),c=(e,t)=>{const i=document.createElementNS("http://www.w3.org/2000/svg",e);return t&&s(i,t),i},h={contain:"xMidYMid meet",cover:"xMidYMid slice"},l={left:"start",center:"middle",right:"end"},d=e=>t=>c(e,{id:t.id}),p={image:e=>{const t=c("image",{id:e.id,"stroke-linecap":"round","stroke-linejoin":"round",opacity:"0"});return t.onload=(()=>{t.setAttribute("opacity",e.opacity||1)}),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",e.src),t},rect:d("rect"),ellipse:d("ellipse"),text:d("text"),path:d("path"),line:e=>{const t=c("g",{id:e.id,"stroke-linecap":"round","stroke-linejoin":"round"}),i=c("line");t.appendChild(i);const r=c("path");t.appendChild(r);const a=c("path");return t.appendChild(a),t}},g={rect:e=>s(e,{...e.rect,...e.styles}),ellipse:e=>{const t=e.rect.x+.5*e.rect.width,i=e.rect.y+.5*e.rect.height,r=.5*e.rect.width,a=.5*e.rect.height;return s(e,{cx:t,cy:i,rx:r,ry:a,...e.styles})},image:(e,t)=>{s(e,{...e.rect,...e.styles,preserveAspectRatio:h[t.fit]||"none"})},text:(e,t,i,r)=>{const o=a(t.fontSize,i,r),n=t.fontFamily||"sans-serif",c=t.fontWeight||"normal",h=l[t.textAlign]||"start";s(e,{...e.rect,...e.styles,"stroke-width":0,"font-weight":c,"font-size":o,"font-family":n,"text-anchor":h}),e.text!==t.text&&(e.text=t.text,e.textContent=t.text.length?t.text:" ")},path:(e,t,i,r)=>{s(e,{...e.styles,fill:"none",d:n(t.points.map(e=>({x:a(e.x,i,r,"width"),y:a(e.y,i,r,"height")})))})},line:(o,n,c,h)=>{s(o,{...o.rect,...o.styles,fill:"none"});const l=o.childNodes[0],d=o.childNodes[1],p=o.childNodes[2],g=o.rect,m={x:o.rect.x+o.rect.width,y:o.rect.y+o.rect.height};if(s(l,{x1:g.x,y1:g.y,x2:m.x,y2:m.y}),!n.lineDecoration)return;d.style.display="none",p.style.display="none";const u=(e=>{const t=Math.sqrt(e.x*e.x+e.y*e.y);return 0===t?{x:0,y:0}:r(e.x/t,e.y/t)})({x:m.x-g.x,y:m.y-g.y}),E=a(.05,c,h);if(-1!==n.lineDecoration.indexOf("arrow-begin")){const r=e(u,E),a=t(g,r),o=i(g,2,a),n=i(g,-2,a);s(d,{style:"display:block;",d:`M${o.x},${o.y} L${g.x},${g.y} L${n.x},${n.y}`})}if(-1!==n.lineDecoration.indexOf("arrow-end")){const r=e(u,-E),a=t(m,r),o=i(m,2,a),n=i(m,-2,a);s(p,{style:"display:block;",d:`M${o.x},${o.y} L${m.x},${m.y} L${n.x},${n.y}`})}}},m=(e,t,i,r,n)=>{"path"!==t&&(e.rect=((e,t,i=1)=>{let r=a(e.x,t,i,"width")||a(e.left,t,i,"width"),n=a(e.y,t,i,"height")||a(e.top,t,i,"height"),s=a(e.width,t,i,"width"),c=a(e.height,t,i,"height"),h=a(e.right,t,i,"width"),l=a(e.bottom,t,i,"height");return o(n)||(n=o(c)&&o(l)?t.height-c-l:l),o(r)||(r=o(s)&&o(h)?t.width-s-h:h),o(s)||(s=o(r)&&o(h)?t.width-r-h:0),o(c)||(c=o(n)&&o(l)?t.height-n-l:0),{x:r||0,y:n||0,width:s||0,height:c||0}})(i,r,n)),e.styles=((e,t,i)=>{const r=e.borderStyle||e.lineStyle||"solid",o=e.backgroundColor||e.fontColor||"transparent",n=e.borderColor||e.lineColor||"transparent",s=a(e.borderWidth||e.lineWidth,t,i);return{"stroke-linecap":e.lineCap||"round","stroke-linejoin":e.lineJoin||"round","stroke-width":s||0,"stroke-dasharray":"string"==typeof r?"":r.map(e=>a(e,t,i)).join(","),stroke:n,fill:o,opacity:e.opacity||1}})(i,r,n),g[t](e,i,r,n)},u=["x","y","left","top","right","bottom","width","height"],E=e=>{const[t,i]=e,r=i.points?{}:u.reduce((e,t)=>(e[t]=(e=>"string"==typeof e&&/%/.test(e)?parseFloat(e)/100:e)(i[t]),e),{});return[t,{zIndex:0,...i,...r}]},f=(e,t)=>e[1].zIndex>t[1].zIndex?1:e[1].zIndex<t[1].zIndex?-1:0,y=e=>e.utils.createView({name:"image-preview-markup",tag:"svg",ignoreRect:!0,mixins:{apis:["width","height","crop","markup","resize","dirty"]},write:({root:e,props:t})=>{if(!t.dirty)return;const{crop:i,resize:r,markup:a}=t,o=t.width,n=t.height;let s=i.width,c=i.height;if(r){const{size:e}=r;let t=e&&e.width,i=e&&e.height;const a=r.mode,o=r.upscale;t&&!i&&(i=t),i&&!t&&(t=i);const n=s<t&&c<i;if(!n||n&&o){let e=t/s,r=i/c;if("force"===a)s=t,c=i;else{let t;"cover"===a?t=Math.max(e,r):"contain"===a&&(t=Math.min(e,r)),s*=t,c*=t}}}const h={width:o,height:n};e.element.setAttribute("width",h.width),e.element.setAttribute("height",h.height);const l=Math.min(o/s,n/c);e.element.innerHTML="";const d=e.query("GET_IMAGE_PREVIEW_MARKUP_FILTER");a.filter(d).map(E).sort(f).forEach(t=>{const[i,r]=t,a=((e,t)=>p[e](t))(i,r);m(a,i,r,h,l),e.element.appendChild(a)})}}),w=(e,t)=>({x:e,y:t}),_=(e,t)=>w(e.x-t.x,e.y-t.y),I=(e,t)=>Math.sqrt(((e,t)=>((e,t)=>e.x*t.x+e.y*t.y)(_(e,t),_(e,t)))(e,t)),M=(e,t)=>{const i=e,r=t,a=1.5707963267948966-t,o=Math.sin(1.5707963267948966),n=Math.sin(r),s=Math.sin(a),c=Math.cos(a),h=i/o;return w(c*(h*n),c*(h*s))},x=(e,t,i,r)=>{const a=r.x>.5?1-r.x:r.x,o=r.y>.5?1-r.y:r.y,n=2*a*e.width,s=2*o*e.height,c=((e,t)=>{const i=e.width,r=e.height,a=M(i,t),o=M(r,t),n=w(e.x+Math.abs(a.x),e.y-Math.abs(a.y)),s=w(e.x+e.width+Math.abs(o.y),e.y+Math.abs(o.x)),c=w(e.x-Math.abs(o.y),e.y+e.height-Math.abs(o.x));return{width:I(n,s),height:I(n,c)}})(t,i);return Math.max(c.width/n,c.height/s)},T=(e,t)=>{let i=e.width,r=i*t;return r>e.height&&(i=(r=e.height)/t),{x:.5*(e.width-i),y:.5*(e.height-r),width:i,height:r}},A=(e,t={})=>{let{zoom:i,rotation:r,center:a,aspectRatio:o}=t;o||(o=e.height/e.width);const n=((e,t,i=1)=>{const r=e.height/e.width;let a=t,o=1,n=r;n>a&&(o=(n=a)/r);const s=Math.max(1/o,a/n),c=e.width/(i*s*o);return{width:c,height:c*t}})(e,o,i),s={x:.5*n.width,y:.5*n.height},c={x:0,y:0,width:n.width,height:n.height,center:s},h=void 0===t.scaleToFit||t.scaleToFit,l=i*x(e,T(c,o),r,h?a:{x:.5,y:.5});return{widthFloat:n.width/l,heightFloat:n.height/l,width:Math.round(n.width/l),height:Math.round(n.height/l)}},R={type:"spring",stiffness:.5,damping:.45,mass:10},C=e=>e.utils.createView({name:"image-clip",tag:"div",ignoreRect:!0,mixins:{apis:["crop","markup","resize","width","height","dirty","background"],styles:["width","height","opacity"],animations:{opacity:{type:"tween",duration:250}}},didWriteView:function({root:e,props:t}){t.background&&(e.element.style.backgroundColor=t.background)},create:({root:t,props:i})=>{t.ref.image=t.appendChildView(t.createChildView((e=>e.utils.createView({name:"image-canvas-wrapper",tag:"div",ignoreRect:!0,mixins:{apis:["crop","width","height"],styles:["originX","originY","translateX","translateY","scaleX","scaleY","rotateZ"],animations:{originX:R,originY:R,scaleX:R,scaleY:R,translateX:R,translateY:R,rotateZ:R}},create:({root:t,props:i})=>{i.width=i.image.width,i.height=i.image.height,t.ref.bitmap=t.appendChildView(t.createChildView((e=>e.utils.createView({name:"image-bitmap",ignoreRect:!0,mixins:{styles:["scaleX","scaleY"]},create:({root:e,props:t})=>{e.appendChild(t.image)}}))(e),{image:i.image}))},write:({root:e,props:t})=>{const{flip:i}=t.crop,{bitmap:r}=e.ref;r.scaleX=i.horizontal?-1:1,r.scaleY=i.vertical?-1:1}}))(e),Object.assign({},i))),t.ref.createMarkup=(()=>{t.ref.markup||(t.ref.markup=t.appendChildView(t.createChildView(y(e),Object.assign({},i))))}),t.ref.destroyMarkup=(()=>{t.ref.markup&&(t.removeChildView(t.ref.markup),t.ref.markup=null)});const r=t.query("GET_IMAGE_PREVIEW_TRANSPARENCY_INDICATOR");null!==r&&(t.element.dataset.transparencyIndicator="grid"===r?r:"color")},write:({root:e,props:t,shouldOptimize:i})=>{const{crop:r,markup:a,resize:o,dirty:n,width:s,height:c}=t;e.ref.image.crop=r;const h={x:0,y:0,width:s,height:c,center:{x:.5*s,y:.5*c}},l={width:e.ref.image.width,height:e.ref.image.height},d={x:r.center.x*l.width,y:r.center.y*l.height},p={x:h.center.x-l.width*r.center.x,y:h.center.y-l.height*r.center.y},g=2*Math.PI+r.rotation%(2*Math.PI),m=r.aspectRatio||l.height/l.width,u=void 0===r.scaleToFit||r.scaleToFit,E=x(l,T(h,m),g,u?r.center:{x:.5,y:.5}),f=r.zoom*E;a&&a.length?(e.ref.createMarkup(),e.ref.markup.width=s,e.ref.markup.height=c,e.ref.markup.resize=o,e.ref.markup.dirty=n,e.ref.markup.markup=a,e.ref.markup.crop=A(l,r)):e.ref.markup&&e.ref.destroyMarkup();const y=e.ref.image;if(i)return y.originX=null,y.originY=null,y.translateX=null,y.translateY=null,y.rotateZ=null,y.scaleX=null,void(y.scaleY=null);y.originX=d.x,y.originY=d.y,y.translateX=p.x,y.translateY=p.y,y.rotateZ=g,y.scaleX=f,y.scaleY=f}});let D=0;const G=function(){self.onmessage=(e=>{createImageBitmap(e.data.message.file).then(t=>{self.postMessage({id:e.data.id,message:t},[t])})})},P=function(){self.onmessage=(e=>{const t=e.data.message.imageData,i=e.data.message.colorMatrix,r=t.data,a=r.length,o=i[0],n=i[1],s=i[2],c=i[3],h=i[4],l=i[5],d=i[6],p=i[7],g=i[8],m=i[9],u=i[10],E=i[11],f=i[12],y=i[13],w=i[14],_=i[15],I=i[16],M=i[17],x=i[18],T=i[19];let A=0,R=0,C=0,D=0,G=0;for(;A<a;A+=4)R=r[A]/255,C=r[A+1]/255,D=r[A+2]/255,G=r[A+3]/255,r[A]=Math.max(0,Math.min(255*(R*o+C*n+D*s+G*c+h),255)),r[A+1]=Math.max(0,Math.min(255*(R*l+C*d+D*p+G*g+m),255)),r[A+2]=Math.max(0,Math.min(255*(R*u+C*E+D*f+G*y+w),255)),r[A+3]=Math.max(0,Math.min(255*(R*_+C*I+D*M+G*x+T),255));self.postMessage({id:e.data.id,message:t},[t.data.buffer])})},v={1:()=>[1,0,0,1,0,0],2:e=>[-1,0,0,1,e,0],3:(e,t)=>[-1,0,0,-1,e,t],4:(e,t)=>[1,0,0,-1,0,t],5:()=>[0,1,1,0,0,0],6:(e,t)=>[0,1,-1,0,t,0],7:(e,t)=>[0,-1,-1,0,t,e],8:e=>[0,-1,1,0,0,e]},k=(e,t,i,r)=>{t=Math.round(t),i=Math.round(i);const a=document.createElement("canvas");a.width=t,a.height=i;const o=a.getContext("2d");return r>=5&&r<=8&&([t,i]=[i,t]),((e,t,i,r)=>{-1!==r&&e.transform.apply(e,v[r](t,i))})(o,t,i,r),o.drawImage(e,0,0,t,i),a},V=e=>/^image/.test(e.type)&&!/svg/.test(e.type),O=e=>{const t=Math.min(10/e.width,10/e.height),i=document.createElement("canvas"),r=i.getContext("2d"),a=i.width=Math.ceil(e.width*t),o=i.height=Math.ceil(e.height*t);r.drawImage(e,0,0,a,o);let n=null;try{n=r.getImageData(0,0,a,o).data}catch(e){return null}const s=n.length;let c=0,h=0,l=0,d=0;for(;d<s;d+=4)c+=n[d]*n[d],h+=n[d+1]*n[d+1],l+=n[d+2]*n[d+2];return{r:c=L(c,s),g:h=L(h,s),b:l=L(l,s)}},L=(e,t)=>Math.floor(Math.sqrt(e/(t/4))),S=(e,t)=>{return(t=t||document.createElement("canvas")).width=e.width,t.height=e.height,t.getContext("2d").drawImage(e,0,0),t},N=e=>{const t=(e=>e.utils.createView({name:"image-preview-overlay",tag:"div",ignoreRect:!0,create:({root:e,props:t})=>{let i='<svg width="500" height="200" viewBox="0 0 500 200" preserveAspectRatio="none">\n    <defs>\n        <radialGradient id="gradient-__UID__" cx=".5" cy="1.25" r="1.15">\n            <stop offset=\'50%\' stop-color=\'#000000\'/>\n            <stop offset=\'56%\' stop-color=\'#0a0a0a\'/>\n            <stop offset=\'63%\' stop-color=\'#262626\'/>\n            <stop offset=\'69%\' stop-color=\'#4f4f4f\'/>\n            <stop offset=\'75%\' stop-color=\'#808080\'/>\n            <stop offset=\'81%\' stop-color=\'#b1b1b1\'/>\n            <stop offset=\'88%\' stop-color=\'#dadada\'/>\n            <stop offset=\'94%\' stop-color=\'#f6f6f6\'/>\n            <stop offset=\'100%\' stop-color=\'#ffffff\'/>\n        </radialGradient>\n        <mask id="mask-__UID__">\n            <rect x="0" y="0" width="500" height="200" fill="url(#gradient-__UID__)"></rect>\n        </mask>\n    </defs>\n    <rect x="0" width="500" height="200" fill="currentColor" mask="url(#mask-__UID__)"></rect>\n</svg>';if(document.querySelector("base")){const e=new URL(window.location.href.replace(window.location.hash,"")).href;i=i.replace(/url\(\#/g,"url("+e+"#")}D++,e.element.classList.add(`filepond--image-preview-overlay-${t.status}`),e.element.innerHTML=i.replace(/__UID__/g,D)},mixins:{styles:["opacity"],animations:{opacity:{type:"spring",mass:25}}}}))(e),i=(e=>e.utils.createView({name:"image-preview",tag:"div",ignoreRect:!0,mixins:{apis:["image","crop","markup","resize","dirty","background"],styles:["translateY","scaleX","scaleY","opacity"],animations:{scaleX:R,scaleY:R,translateY:R,opacity:{type:"tween",duration:400}}},create:({root:t,props:i})=>{t.ref.clip=t.appendChildView(t.createChildView(C(e),{id:i.id,image:i.image,crop:i.crop,markup:i.markup,resize:i.resize,dirty:i.dirty,background:i.background}))},write:({root:e,props:t,shouldOptimize:i})=>{const{clip:r}=e.ref,{image:a,crop:o,markup:n,resize:s,dirty:c}=t;if(r.crop=o,r.markup=n,r.resize=s,r.dirty=c,r.opacity=i?0:1,i||e.rect.element.hidden)return;const h=a.height/a.width;let l=o.aspectRatio||h;const d=e.rect.inner.width,p=e.rect.inner.height;let g=e.query("GET_IMAGE_PREVIEW_HEIGHT");const m=e.query("GET_IMAGE_PREVIEW_MIN_HEIGHT"),u=e.query("GET_IMAGE_PREVIEW_MAX_HEIGHT"),E=e.query("GET_PANEL_ASPECT_RATIO"),f=e.query("GET_ALLOW_MULTIPLE");E&&!f&&(g=d*E,l=E);let y=null!==g?g:Math.max(m,Math.min(d*l,u)),w=y/l;w>d&&(y=(w=d)*l),y>p&&(y=p,w=p/l),r.width=w,r.height=y}}))(e),{createWorker:r}=e.utils,a=(e,t,i)=>new Promise(a=>{e.ref.imageData||(e.ref.imageData=i.getContext("2d").getImageData(0,0,i.width,i.height));const o=(e=>{let t;try{t=new ImageData(e.width,e.height)}catch(i){t=document.createElement("canvas").getContext("2d").createImageData(e.width,e.height)}return t.data.set(new Uint8ClampedArray(e.data)),t})(e.ref.imageData);if(!t||20!==t.length)return i.getContext("2d").putImageData(o,0,0),a();const n=r(P);n.post({imageData:o,colorMatrix:t},e=>{i.getContext("2d").putImageData(e,0,0),n.terminate(),a()},[o.data.buffer])}),o=({root:e,props:t,image:r})=>{const a=t.id,o=e.query("GET_ITEM",{id:a});if(!o)return;const n=o.getMetadata("crop")||{center:{x:.5,y:.5},flip:{horizontal:!1,vertical:!1},zoom:1,rotation:0,aspectRatio:null},s=e.query("GET_IMAGE_TRANSFORM_CANVAS_BACKGROUND_COLOR");let c,h,l=!1;e.query("GET_IMAGE_PREVIEW_MARKUP_SHOW")&&(c=o.getMetadata("markup")||[],h=o.getMetadata("resize"),l=!0);const d=e.appendChildView(e.createChildView(i,{id:a,image:r,crop:n,resize:h,markup:c,dirty:l,background:s,opacity:0,scaleX:1.15,scaleY:1.15,translateY:15}),e.childViews.length);e.ref.images.push(d),d.opacity=1,d.scaleX=1,d.scaleY=1,d.translateY=0,setTimeout(()=>{e.dispatch("DID_IMAGE_PREVIEW_SHOW",{id:a})},250)},n=({root:e})=>{e.ref.overlayShadow.opacity=1,e.ref.overlayError.opacity=0,e.ref.overlaySuccess.opacity=0},s=({root:e})=>{e.ref.overlayShadow.opacity=.25,e.ref.overlayError.opacity=1};return e.utils.createView({name:"image-preview-wrapper",create:({root:e})=>{e.ref.images=[],e.ref.imageData=null,e.ref.imageViewBin=[],e.ref.overlayShadow=e.appendChildView(e.createChildView(t,{opacity:0,status:"idle"})),e.ref.overlaySuccess=e.appendChildView(e.createChildView(t,{opacity:0,status:"success"})),e.ref.overlayError=e.appendChildView(e.createChildView(t,{opacity:0,status:"failure"}))},styles:["height"],apis:["height"],destroy:({root:e})=>{e.ref.images.forEach(e=>{e.image.width=1,e.image.height=1})},didWriteView:({root:e})=>{e.ref.images.forEach(e=>{e.dirty=!1})},write:e.utils.createRoute({DID_IMAGE_PREVIEW_DRAW:({root:e})=>{const t=e.ref.images[e.ref.images.length-1];t.translateY=0,t.scaleX=1,t.scaleY=1,t.opacity=1},DID_IMAGE_PREVIEW_CONTAINER_CREATE:({root:e,props:t})=>{const{id:i}=t,r=e.query("GET_ITEM",i);r&&((e,t)=>{let i=new Image;i.onload=(()=>{const e=i.naturalWidth,r=i.naturalHeight;i=null,t(e,r)}),i.src=e})(URL.createObjectURL(r.file),(t,r)=>{e.dispatch("DID_IMAGE_PREVIEW_CALCULATE_SIZE",{id:i,width:t,height:r})})},DID_FINISH_CALCULATE_PREVIEWSIZE:({root:e,props:t})=>{const{id:i}=t,n=e.query("GET_ITEM",i);if(!n)return;const s=URL.createObjectURL(n.file),c=()=>{(e=>new Promise((t,i)=>{const r=new Image;r.crossOrigin="Anonymous",r.onload=(()=>{t(r)}),r.onerror=(e=>{i(e)}),r.src=e}))(s).then(h)},h=i=>{URL.revokeObjectURL(s);const r=(n.getMetadata("exif")||{}).orientation||-1;let{width:c,height:h}=i;if(!c||!h)return;r>=5&&r<=8&&([c,h]=[h,c]);const l=Math.max(1,.75*window.devicePixelRatio),d=e.query("GET_IMAGE_PREVIEW_ZOOM_FACTOR")*l,p=h/c,g=e.rect.element.width,m=e.rect.element.height;let u=g,E=u*p;p>1?E=(u=Math.min(c,g*d))*p:u=(E=Math.min(h,m*d))/p;const f=k(i,u,E,r),y=()=>{const r=e.query("GET_IMAGE_PREVIEW_CALCULATE_AVERAGE_IMAGE_COLOR")?O(data):null;n.setMetadata("color",r,!0),"close"in i&&i.close(),e.ref.overlayShadow.opacity=1,o({root:e,props:t,image:f})},w=n.getMetadata("filter");w?a(e,w,f).then(y):y()};if((e=>{const t=window.navigator.userAgent.match(/Firefox\/([0-9]+)\./);return!((t?parseInt(t[1]):null)<=58)&&"createImageBitmap"in window&&V(e)})(n.file)){const e=r(G);e.post({file:n.file},t=>{e.terminate(),t?h(t):c()})}else c()},DID_UPDATE_ITEM_METADATA:({root:e,props:t,action:i})=>{if(!/crop|filter|markup|resize/.test(i.change.key))return;if(!e.ref.images.length)return;const r=e.query("GET_ITEM",{id:t.id});if(r)if(/filter/.test(i.change.key)){const t=e.ref.images[e.ref.images.length-1];a(e,i.change.value,t.image)}else if(/crop|markup|resize/.test(i.change.key)){const i=r.getMetadata("crop"),a=e.ref.images[e.ref.images.length-1];if(i&&i.aspectRatio&&a.crop&&a.crop.aspectRatio&&Math.abs(i.aspectRatio-a.crop.aspectRatio)>1e-5){const i=(({root:e})=>{const t=e.ref.images.shift();return t.opacity=0,t.translateY=-15,e.ref.imageViewBin.push(t),t})({root:e});o({root:e,props:t,image:S(i.image)})}else(({root:e,props:t})=>{const i=e.query("GET_ITEM",{id:t.id});if(!i)return;const r=e.ref.images[e.ref.images.length-1];r.crop=i.getMetadata("crop"),r.background=e.query("GET_IMAGE_TRANSFORM_CANVAS_BACKGROUND_COLOR"),e.query("GET_IMAGE_PREVIEW_MARKUP_SHOW")&&(r.dirty=!0,r.resize=i.getMetadata("resize"),r.markup=i.getMetadata("markup"))})({root:e,props:t})}},DID_THROW_ITEM_LOAD_ERROR:s,DID_THROW_ITEM_PROCESSING_ERROR:s,DID_THROW_ITEM_INVALID:s,DID_COMPLETE_ITEM_PROCESSING:({root:e})=>{e.ref.overlayShadow.opacity=.25,e.ref.overlaySuccess.opacity=1},DID_START_ITEM_PROCESSING:n,DID_REVERT_ITEM_PROCESSING:n},({root:e})=>{const t=e.ref.imageViewBin.filter(e=>0===e.opacity);e.ref.imageViewBin=e.ref.imageViewBin.filter(e=>e.opacity>0),t.forEach(t=>((e,t)=>{e.removeChildView(t),t.image.width=1,t.image.height=1,t._destroy()})(e,t)),t.length=0})})},b=e=>{const{addFilter:t,utils:i}=e,{Type:r,createRoute:a,isFile:o}=i,n=N(e);return t("CREATE_VIEW",e=>{const{is:t,view:i,query:r}=e;if(!t("file")||!r("GET_ALLOW_IMAGE_PREVIEW"))return;const s=({root:e})=>{e.ref.shouldRescale=!0};i.registerWriter(a({DID_RESIZE_ROOT:s,DID_STOP_RESIZE:s,DID_LOAD_ITEM:({root:e,props:t})=>{const{id:a}=t,s=r("GET_ITEM",a);if(!s||!o(s.file)||s.archived)return;const c=s.file;if(!(e=>/^image/.test(e.type))(c))return;if(!r("GET_IMAGE_PREVIEW_FILTER_ITEM")(s))return;const h="createImageBitmap"in(window||{}),l=r("GET_IMAGE_PREVIEW_MAX_FILE_SIZE");if(!h&&l&&c.size>l)return;e.ref.imagePreview=i.appendChildView(i.createChildView(n,{id:a}));const d=e.query("GET_IMAGE_PREVIEW_HEIGHT");d&&e.dispatch("DID_UPDATE_PANEL_HEIGHT",{id:s.id,height:d});const p=!h&&c.size>r("GET_IMAGE_PREVIEW_MAX_INSTANT_PREVIEW_FILE_SIZE");e.dispatch("DID_IMAGE_PREVIEW_CONTAINER_CREATE",{id:a},p)},DID_IMAGE_PREVIEW_CALCULATE_SIZE:({root:e,action:t})=>{e.ref.imageWidth=t.width,e.ref.imageHeight=t.height,e.ref.shouldRescale=!0,e.ref.shouldDrawPreview=!0,e.dispatch("KICK")},DID_UPDATE_ITEM_METADATA:({root:e,action:t})=>{"crop"===t.change.key&&(e.ref.shouldRescale=!0)}},({root:e,props:t})=>{e.ref.imagePreview&&(e.rect.element.hidden||(e.ref.shouldRescale&&(((e,t)=>{if(!e.ref.imagePreview)return;let{id:i}=t;const r=e.query("GET_ITEM",{id:i});if(!r)return;const a=e.query("GET_PANEL_ASPECT_RATIO"),o=e.query("GET_ITEM_PANEL_ASPECT_RATIO"),n=e.query("GET_IMAGE_PREVIEW_HEIGHT");if(a||o||n)return;let{imageWidth:s,imageHeight:c}=e.ref;if(!s||!c)return;const h=e.query("GET_IMAGE_PREVIEW_MIN_HEIGHT"),l=e.query("GET_IMAGE_PREVIEW_MAX_HEIGHT"),d=(r.getMetadata("exif")||{}).orientation||-1;if(d>=5&&d<=8&&([s,c]=[c,s]),!V(r.file)||e.query("GET_IMAGE_PREVIEW_UPSCALE")){const e=2048/s;s*=e,c*=e}const p=c/s,g=(r.getMetadata("crop")||{}).aspectRatio||p;let m=Math.max(h,Math.min(c,l));const u=e.rect.element.width,E=Math.min(u*g,m);e.dispatch("DID_UPDATE_PANEL_HEIGHT",{id:r.id,height:E})})(e,t),e.ref.shouldRescale=!1),e.ref.shouldDrawPreview&&(requestAnimationFrame(()=>{requestAnimationFrame(()=>{e.dispatch("DID_FINISH_CALCULATE_PREVIEWSIZE",{id:t.id})})}),e.ref.shouldDrawPreview=!1)))}))}),{options:{allowImagePreview:[!0,r.BOOLEAN],imagePreviewFilterItem:[()=>!0,r.FUNCTION],imagePreviewHeight:[null,r.INT],imagePreviewMinHeight:[44,r.INT],imagePreviewMaxHeight:[256,r.INT],imagePreviewMaxFileSize:[null,r.INT],imagePreviewZoomFactor:[2,r.INT],imagePreviewUpscale:[!1,r.BOOLEAN],imagePreviewMaxInstantPreviewFileSize:[1e6,r.INT],imagePreviewTransparencyIndicator:[null,r.STRING],imagePreviewCalculateAverageImageColor:[!1,r.BOOLEAN],imagePreviewMarkupShow:[!0,r.BOOLEAN],imagePreviewMarkupFilter:[()=>!0,r.FUNCTION]}}};"undefined"!=typeof window&&void 0!==window.document&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:b}));export default b;
