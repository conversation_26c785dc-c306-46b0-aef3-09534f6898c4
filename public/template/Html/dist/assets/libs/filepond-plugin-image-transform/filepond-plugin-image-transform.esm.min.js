/*!
 * FilePondPluginImageTransform 3.8.7
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */

/* eslint-disable */

const t={jpeg:"jpg","svg+xml":"svg"},e={1:()=>[1,0,0,1,0,0],2:t=>[-1,0,0,1,t,0],3:(t,e)=>[-1,0,0,-1,t,e],4:(t,e)=>[1,0,0,-1,0,e],5:()=>[0,1,1,0,0,0],6:(t,e)=>[0,1,-1,0,e,0],7:(t,e)=>[0,-1,-1,0,e,t],8:t=>[0,-1,1,0,0,t]},n=(t,e)=>({x:t,y:e}),i=(t,e)=>n(t.x-e.x,t.y-e.y),o=(t,e)=>Math.sqrt(((t,e)=>((t,e)=>t.x*e.x+t.y*e.y)(i(t,e),i(t,e)))(t,e)),r=(t,e)=>{const i=t,o=e,r=1.5707963267948966-e,a=Math.sin(1.5707963267948966),l=Math.sin(o),s=Math.sin(r),h=Math.cos(r),c=i/a;return n(h*(c*l),h*(c*s))},a=(t,e,i=0,a={x:.5,y:.5})=>{const l=a.x>.5?1-a.x:a.x,s=a.y>.5?1-a.y:a.y,h=2*l*t.width,c=2*s*t.height,d=((t,e)=>{const i=t.width,a=t.height,l=r(i,e),s=r(a,e),h=n(t.x+Math.abs(l.x),t.y-Math.abs(l.y)),c=n(t.x+t.width+Math.abs(s.y),t.y+Math.abs(s.x)),d=n(t.x-Math.abs(s.y),t.y+t.height-Math.abs(s.x));return{width:o(h,c),height:o(h,d)}})(e,i);return Math.max(d.width/h,d.height/c)},l=(t,e)=>{let n=t.width,i=n*e;return i>t.height&&(n=(i=t.height)/e),{x:.5*(t.width-n),y:.5*(t.height-i),width:n,height:i}},s=(t,e,n=1)=>{const i=t.height/t.width;let o=e,r=1,a=i;a>o&&(r=(a=o)/i);const l=Math.max(1/r,o/a),s=t.width/(n*l*r);return{width:s,height:s*e}},h=t=>{t.width=1,t.height=1,t.getContext("2d").clearRect(0,0,1,1)},c=t=>t&&(t.horizontal||t.vertical),d=(t,n,i)=>{if(n<=1&&!c(i))return t.width=t.naturalWidth,t.height=t.naturalHeight,t;const o=document.createElement("canvas"),r=t.naturalWidth,a=t.naturalHeight,l=n>=5&&n<=8;l?(o.width=a,o.height=r):(o.width=r,o.height=a);const s=o.getContext("2d");if(n&&s.transform.apply(s,((t,n,i)=>(-1===i&&(i=1),e[i](t,n)))(r,a,n)),c(i)){const t=[1,0,0,1,0,0];(!l&&i.horizontal||l&i.vertical)&&(t[0]=-1,t[4]=r),(!l&&i.vertical||l&&i.horizontal)&&(t[3]=-1,t[5]=a),s.transform(...t)}return s.drawImage(t,0,0,r,a),o};(()=>"undefined"!=typeof window&&void 0!==window.document)()&&(HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,"toBlob",{value:function(t,e,n){var i=this.toDataURL(e,n).split(",")[1];setTimeout(function(){for(var n=atob(i),o=n.length,r=new Uint8Array(o),a=0;a<o;a++)r[a]=n.charCodeAt(a);t(new Blob([r],{type:e||"image/png"}))})}}));const g=(t,e)=>p(t.x*e,t.y*e),u=(t,e)=>p(t.x+e.x,t.y+e.y),m=t=>{const e=Math.sqrt(t.x*t.x+t.y*t.y);return 0===e?{x:0,y:0}:p(t.x/e,t.y/e)},y=(t,e,n)=>{const i=Math.cos(e),o=Math.sin(e),r=p(t.x-n.x,t.y-n.y);return p(n.x+i*r.x-o*r.y,n.y+o*r.x+i*r.y)},p=(t=0,e=0)=>({x:t,y:e}),f=(t,e,n=1,i)=>"string"==typeof t?parseFloat(t)*n:"number"==typeof t?t*(i?e[i]:Math.min(e.width,e.height)):void 0,w=(t,e,n)=>{const i=t.borderStyle||t.lineStyle||"solid",o=t.backgroundColor||t.fontColor||"transparent",r=t.borderColor||t.lineColor||"transparent",a=f(t.borderWidth||t.lineWidth,e,n);return{"stroke-linecap":t.lineCap||"round","stroke-linejoin":t.lineJoin||"round","stroke-width":a||0,"stroke-dasharray":"string"==typeof i?"":i.map(t=>f(t,e,n)).join(","),stroke:r,fill:o,opacity:t.opacity||1}},x=t=>null!=t,M=(t,e,n=1)=>{let i=f(t.x,e,n,"width")||f(t.left,e,n,"width"),o=f(t.y,e,n,"height")||f(t.top,e,n,"height"),r=f(t.width,e,n,"width"),a=f(t.height,e,n,"height"),l=f(t.right,e,n,"width"),s=f(t.bottom,e,n,"height");return x(o)||(o=x(a)&&x(s)?e.height-a-s:s),x(i)||(i=x(r)&&x(l)?e.width-r-l:l),x(r)||(r=x(i)&&x(l)?e.width-i-l:0),x(a)||(a=x(o)&&x(s)?e.height-o-s:0),{x:i||0,y:o||0,width:r||0,height:a||0}},T=t=>t.map((t,e)=>`${0===e?"M":"L"} ${t.x} ${t.y}`).join(" "),A=(t,e)=>Object.keys(e).forEach(n=>t.setAttribute(n,e[n])),b=(t,e)=>{const n=document.createElementNS("http://www.w3.org/2000/svg",t);return e&&A(n,e),n},R={contain:"xMidYMid meet",cover:"xMidYMid slice"},E={left:"start",center:"middle",right:"end"},_=t=>e=>b(t,{id:e.id}),v={image:t=>{const e=b("image",{id:t.id,"stroke-linecap":"round","stroke-linejoin":"round",opacity:"0"});return e.onload=(()=>{e.setAttribute("opacity",t.opacity||1)}),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",t.src),e},rect:_("rect"),ellipse:_("ellipse"),text:_("text"),path:_("path"),line:t=>{const e=b("g",{id:t.id,"stroke-linecap":"round","stroke-linejoin":"round"}),n=b("line");e.appendChild(n);const i=b("path");e.appendChild(i);const o=b("path");return e.appendChild(o),e}},I={rect:t=>A(t,{...t.rect,...t.styles}),ellipse:t=>{const e=t.rect.x+.5*t.rect.width,n=t.rect.y+.5*t.rect.height,i=.5*t.rect.width,o=.5*t.rect.height;return A(t,{cx:e,cy:n,rx:i,ry:o,...t.styles})},image:(t,e)=>{A(t,{...t.rect,...t.styles,preserveAspectRatio:R[e.fit]||"none"})},text:(t,e,n,i)=>{const o=f(e.fontSize,n,i),r=e.fontFamily||"sans-serif",a=e.fontWeight||"normal",l=E[e.textAlign]||"start";A(t,{...t.rect,...t.styles,"stroke-width":0,"font-weight":a,"font-size":o,"font-family":r,"text-anchor":l}),t.text!==e.text&&(t.text=e.text,t.textContent=e.text.length?e.text:" ")},path:(t,e,n,i)=>{A(t,{...t.styles,fill:"none",d:T(e.points.map(t=>({x:f(t.x,n,i,"width"),y:f(t.y,n,i,"height")})))})},line:(t,e,n,i)=>{A(t,{...t.rect,...t.styles,fill:"none"});const o=t.childNodes[0],r=t.childNodes[1],a=t.childNodes[2],l=t.rect,s={x:t.rect.x+t.rect.width,y:t.rect.y+t.rect.height};if(A(o,{x1:l.x,y1:l.y,x2:s.x,y2:s.y}),!e.lineDecoration)return;r.style.display="none",a.style.display="none";const h=m({x:s.x-l.x,y:s.y-l.y}),c=f(.05,n,i);if(-1!==e.lineDecoration.indexOf("arrow-begin")){const t=g(h,c),e=u(l,t),n=y(l,2,e),i=y(l,-2,e);A(r,{style:"display:block;",d:`M${n.x},${n.y} L${l.x},${l.y} L${i.x},${i.y}`})}if(-1!==e.lineDecoration.indexOf("arrow-end")){const t=g(h,-c),e=u(s,t),n=y(s,2,e),i=y(s,-2,e);A(a,{style:"display:block;",d:`M${n.x},${n.y} L${s.x},${s.y} L${i.x},${i.y}`})}}},O=(t,e)=>t[1].zIndex>e[1].zIndex?1:t[1].zIndex<e[1].zIndex?-1:0,N=(t,e={},n,i)=>new Promise(o=>{const{background:r=null}=i,s=new FileReader;s.onloadend=(()=>{const t=s.result,i=document.createElement("div");i.style.cssText="position:absolute;pointer-events:none;width:0;height:0;visibility:hidden;",i.innerHTML=t;const h=i.querySelector("svg");document.body.appendChild(i);const c=h.getBBox();i.parentNode.removeChild(i);const d=i.querySelector("title"),g=h.getAttribute("viewBox")||"",u=h.getAttribute("width")||"",m=h.getAttribute("height")||"";let y=parseFloat(u)||null,p=parseFloat(m)||null;const f=(u.match(/[a-z]+/)||[])[0]||"",x=(m.match(/[a-z]+/)||[])[0]||"",T=g.split(" ").map(parseFloat),A=T.length?{x:T[0],y:T[1],width:T[2],height:T[3]}:c;let b=null!=y?y:A.width,R=null!=p?p:A.height;h.style.overflow="visible",h.setAttribute("width",b),h.setAttribute("height",R);let E="";if(n&&n.length){const t={width:b,height:R};E=`\n\n<g>${(E=n.sort(O).reduce((e,n)=>{const i=((t,e)=>v[t](e))(n[0],n[1]);return((t,e,n,i,o)=>{"path"!==e&&(t.rect=M(n,i,o)),t.styles=w(n,i,o),I[e](t,n,i,o)})(i,n[0],n[1],t),i.removeAttribute("id"),1===i.getAttribute("opacity")&&i.removeAttribute("opacity"),e+"\n"+i.outerHTML+"\n"},"")).replace(/&nbsp;/g," ")}</g>\n\n`}const _=e.aspectRatio||R/b,N=b,L=N*_,C=void 0===e.scaleToFit||e.scaleToFit,U=e.center?e.center.x:.5,S=e.center?e.center.y:.5,P=a({width:b,height:R},l({width:N,height:L},_),e.rotation,C?{x:U,y:S}:{x:.5,y:.5}),$=e.zoom*P,k=.5*N,B=.5*L,F=[`rotate(${e.rotation*(180/Math.PI)} ${k} ${B})`,`translate(${k} ${B})`,`scale(${$})`,`translate(${-k} ${-B})`,`translate(${k-b*U} ${B-R*S})`],G=e.flip&&e.flip.horizontal,z=e.flip&&e.flip.vertical,D=[`scale(${G?-1:1} ${z?-1:1})`,`translate(${G?-b:0} ${z?-R:0})`],j=`<?xml version="1.0" encoding="UTF-8"?>\n<svg width="${N}${f}" height="${L}${x}" \nviewBox="0 0 ${N} ${L}" ${r?'style="background:'+r+'" ':""}\npreserveAspectRatio="xMinYMin"\nxmlns:xlink="http://www.w3.org/1999/xlink"\nxmlns="http://www.w3.org/2000/svg">\n\x3c!-- Generated by PQINA - https://pqina.nl/ --\x3e\n<title>${d?d.textContent:""}</title>\n<g transform="${F.join(" ")}">\n<g transform="${D.join(" ")}">\n${h.outerHTML}${E}\n</g>\n</g>\n</svg>`;o(j)}),s.readAsText(t)}),L=()=>{const t={resize:function(t,e){let{mode:n="contain",upscale:i=!1,width:o,height:a,matrix:h}=e;if(h=!h||l(h)?null:h,!o&&!a)return s(t,h);null===o?o=a:null===a&&(a=o);if("force"!==n){let e=o/t.width,r=a/t.height,l=1;if("cover"===n?l=Math.max(e,r):"contain"===n&&(l=Math.min(e,r)),l>1&&!1===i)return s(t,h);o=t.width*l,a=t.height*l}const c=t.width,d=t.height,g=Math.round(o),u=Math.round(a),m=t.data,y=new Uint8ClampedArray(g*u*4),p=c/g,f=d/u,w=Math.ceil(.5*p),x=Math.ceil(.5*f);for(let t=0;t<u;t++)for(let e=0;e<g;e++){let n=4*(e+t*g),i=0,o=0,a=0,l=0,s=0,d=0,u=0,M=(t+.5)*f;for(let n=Math.floor(t*f);n<(t+1)*f;n++){let t=Math.abs(M-(n+.5))/x,r=(e+.5)*p,h=t*t;for(let t=Math.floor(e*p);t<(e+1)*p;t++){let e=Math.abs(r-(t+.5))/w,g=Math.sqrt(h+e*e);if(g>=-1&&g<=1&&(i=2*g*g*g-3*g*g+1)>0){let r=m[(e=4*(t+n*c))+3];u+=i*r,a+=i,r<255&&(i=i*r/250),l+=i*m[e],s+=i*m[e+1],d+=i*m[e+2],o+=i}}}y[n]=l/o,y[n+1]=s/o,y[n+2]=d/o,y[n+3]=u/a,h&&r(n,y,h)}return{data:y,width:g,height:u}},filter:s},e=(e,n)=>{let i=e.transforms,o=null;if(i.forEach(t=>{"filter"===t.type&&(o=t)}),o){let t=null;i.forEach(e=>{"resize"===e.type&&(t=e)}),t&&(t.data.matrix=o.data,i=i.filter(t=>"filter"!==t.type))}n(((e,n)=>(e.forEach(e=>{n=t[e.type](n,e.data)}),n))(i,e.imageData))};self.onmessage=(t=>{e(t.data.message,e=>{self.postMessage({id:t.data.id,message:e},[e.data.buffer])})});const n=1,i=1,o=1;function r(t,e,r){const a=e[t]/255,l=e[t+1]/255,s=e[t+2]/255,h=e[t+3]/255,c=a*r[0]+l*r[1]+s*r[2]+h*r[3]+r[4],d=a*r[5]+l*r[6]+s*r[7]+h*r[8]+r[9],g=a*r[10]+l*r[11]+s*r[12]+h*r[13]+r[14],u=a*r[15]+l*r[16]+s*r[17]+h*r[18]+r[19],m=Math.max(0,c*u)+n*(1-u),y=Math.max(0,d*u)+i*(1-u),p=Math.max(0,g*u)+o*(1-u);e[t]=255*Math.max(0,Math.min(1,m)),e[t+1]=255*Math.max(0,Math.min(1,y)),e[t+2]=255*Math.max(0,Math.min(1,p))}const a=self.JSON.stringify([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0]);function l(t){return self.JSON.stringify(t||[])===a}function s(t,e){if(!e||l(e))return t;const r=t.data,a=r.length,s=e[0],h=e[1],c=e[2],d=e[3],g=e[4],u=e[5],m=e[6],y=e[7],p=e[8],f=e[9],w=e[10],x=e[11],M=e[12],T=e[13],A=e[14],b=e[15],R=e[16],E=e[17],_=e[18],v=e[19];let I=0,O=0,N=0,L=0,C=0,U=0,S=0,P=0,$=0,k=0,B=0,F=0;for(;I<a;I+=4)U=(O=r[I]/255)*s+(N=r[I+1]/255)*h+(L=r[I+2]/255)*c+(C=r[I+3]/255)*d+g,S=O*u+N*m+L*y+C*p+f,P=O*w+N*x+L*M+C*T+A,$=O*b+N*R+L*E+C*_+v,k=Math.max(0,U*$)+n*(1-$),B=Math.max(0,S*$)+i*(1-$),F=Math.max(0,P*$)+o*(1-$),r[I]=255*Math.max(0,Math.min(1,k)),r[I+1]=255*Math.max(0,Math.min(1,B)),r[I+2]=255*Math.max(0,Math.min(1,F));return t}},C=(t,e)=>{if(1165519206!==t.getUint32(e+4,!1))return;e+=4;const n=18761===t.getUint16(e+=6,!1);e+=t.getUint32(e+4,n);const i=t.getUint16(e,n);e+=2;for(let o=0;o<i;o++)if(274===t.getUint16(e+12*o,n))return t.setUint16(e+12*o+8,1,n),!0;return!1},U=t=>new Promise(e=>{const n=new FileReader;n.onload=(()=>e((t=>{const e=new DataView(t);if(65496!==e.getUint16(0))return null;let n,i,o=2,r=!1;for(;o<e.byteLength&&(n=e.getUint16(o,!1),i=e.getUint16(o+2,!1)+2,n>=65504&&n<=65519||65534===n)&&(r||(r=C(e,o)),!(o+i>e.byteLength));)o+=i;return t.slice(0,o)})(n.result)||null)),n.readAsArrayBuffer(t.slice(0,262144))}),S=(t,e)=>{const n=(()=>window.BlobBuilder=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder)();if(n){const i=new n;return i.append(t),i.getBlob(e)}return new Blob([t],{type:e})},P=t=>{const e=new Blob(["(",t.toString(),")()"],{type:"application/javascript"}),n=URL.createObjectURL(e),i=new Worker(n),o=[];return{transfer:()=>{},post:(t,e,n)=>{const r=(()=>Math.random().toString(36).substr(2,9))();o[r]=e,i.onmessage=(t=>{const e=o[t.data.id];e&&(e(t.data.message),delete o[t.data.id])}),i.postMessage({id:r,message:t},n)},terminate:()=>{i.terminate(),URL.revokeObjectURL(n)}}},$=(t,e)=>new Promise(n=>{const i={width:t.width,height:t.height},o=t.getContext("2d");(t=>t.reduce((t,e)=>t.then(t=>e().then(Array.prototype.concat.bind(t))),Promise.resolve([])))(e.sort(O).map(t=>()=>new Promise(e=>{F[t[0]](o,i,t[1],e)&&e()}))).then(()=>n(t))}),k=(t,e)=>{t.beginPath(),t.lineCap=e["stroke-linecap"],t.lineJoin=e["stroke-linejoin"],t.lineWidth=e["stroke-width"],e["stroke-dasharray"].length&&t.setLineDash(e["stroke-dasharray"].split(",")),t.fillStyle=e.fill,t.strokeStyle=e.stroke,t.globalAlpha=e.opacity||1},B=t=>{t.fill(),t.stroke(),t.globalAlpha=1},F={rect:(t,e,n)=>{const i=M(n,e),o=w(n,e);return k(t,o),t.rect(i.x,i.y,i.width,i.height),B(t),!0},ellipse:(t,e,n)=>{const i=M(n,e),o=w(n,e);k(t,o);const r=i.x,a=i.y,l=i.width,s=i.height,h=l/2*.5522848,c=s/2*.5522848,d=r+l,g=a+s,u=r+l/2,m=a+s/2;return t.moveTo(r,m),t.bezierCurveTo(r,m-c,u-h,a,u,a),t.bezierCurveTo(u+h,a,d,m-c,d,m),t.bezierCurveTo(d,m+c,u+h,g,u,g),t.bezierCurveTo(u-h,g,r,m+c,r,m),B(t),!0},image:(t,e,n,i)=>{const o=M(n,e),r=w(n,e);k(t,r);const a=new Image;new URL(n.src,window.location.href).origin!==window.location.origin&&(a.crossOrigin=""),a.onload=(()=>{if("cover"===n.fit){const e=o.width/o.height,n=e>1?a.width:a.height*e,i=e>1?a.width/e:a.height,r=.5*a.width-.5*n,l=.5*a.height-.5*i;t.drawImage(a,r,l,n,i,o.x,o.y,o.width,o.height)}else if("contain"===n.fit){const e=Math.min(o.width/a.width,o.height/a.height),n=e*a.width,i=e*a.height,r=o.x+.5*o.width-.5*n,l=o.y+.5*o.height-.5*i;t.drawImage(a,0,0,a.width,a.height,r,l,n,i)}else t.drawImage(a,0,0,a.width,a.height,o.x,o.y,o.width,o.height);B(t),i()}),a.src=n.src},text:(t,e,n)=>{const i=M(n,e),o=w(n,e);k(t,o);const r=f(n.fontSize,e),a=n.fontFamily||"sans-serif",l=n.fontWeight||"normal",s=n.textAlign||"left";return t.font=`${l} ${r}px ${a}`,t.textAlign=s,t.fillText(n.text,i.x,i.y),B(t),!0},line:(t,e,n)=>{const i=M(n,e),o=w(n,e);k(t,o),t.beginPath();const r={x:i.x,y:i.y},a={x:i.x+i.width,y:i.y+i.height};t.moveTo(r.x,r.y),t.lineTo(a.x,a.y);const l=m({x:a.x-r.x,y:a.y-r.y}),s=.04*Math.min(e.width,e.height);if(-1!==n.lineDecoration.indexOf("arrow-begin")){const e=g(l,s),n=u(r,e),i=y(r,2,n),o=y(r,-2,n);t.moveTo(i.x,i.y),t.lineTo(r.x,r.y),t.lineTo(o.x,o.y)}if(-1!==n.lineDecoration.indexOf("arrow-end")){const e=g(l,-s),n=u(a,e),i=y(a,2,n),o=y(a,-2,n);t.moveTo(i.x,i.y),t.lineTo(a.x,a.y),t.lineTo(o.x,o.y)}return B(t),!0},path:(t,e,n)=>{const i=w(n,e);k(t,i),t.beginPath();const o=n.points.map(t=>({x:f(t.x,e,1,"width"),y:f(t.y,e,1,"height")}));t.moveTo(o[0].x,o[0].y);const r=o.length;for(let e=1;e<r;e++)t.lineTo(o[e].x,o[e].y);return B(t),!0}},G=(t,e,n={})=>new Promise((i,o)=>{if(!t||!(t=>/^image/.test(t.type))(t))return o({status:"not an image file",file:t});const{stripImageHead:r,beforeCreateBlob:c,afterCreateBlob:g,canvasMemoryLimit:u}=n,{crop:m,size:y,filter:p,markup:f,output:w}=e,x=e.image&&e.image.orientation?Math.max(1,Math.min(8,e.image.orientation)):null,M=w&&w.quality,T=null===M?null:M/100,A=w&&w.type||null,b=w&&w.background||null,R=[];!y||"number"!=typeof y.width&&"number"!=typeof y.height||R.push({type:"resize",data:y}),p&&20===p.length&&R.push({type:"filter",data:p});const E=t=>{const e=g?g(t):t;Promise.resolve(e).then(i)},_=(e,n)=>{const i=(t=>{const e=document.createElement("canvas");return e.width=t.width,e.height=t.height,e.getContext("2d").putImageData(t,0,0),e})(e),a=f.length?$(i,f):i;Promise.resolve(a).then(e=>{((t,e,n=null)=>new Promise(i=>{const o=n?n(t):t;Promise.resolve(o).then(t=>{t.toBlob(i,e.type,e.quality)})}))(e,n,c).then(n=>{if(h(e),r)return E(n);U(t).then(t=>{null!==t&&(n=new Blob([t,n.slice(20)],{type:n.type})),E(n)})}).catch(o)})};if(/svg/.test(t.type)&&null===A)return N(t,m,f,{background:b}).then(t=>{i(S(t,"image/svg+xml"))});const v=URL.createObjectURL(t);(t=>new Promise((e,n)=>{const i=new Image;i.onload=(()=>{e(i)}),i.onerror=(t=>{n(t)}),i.src=t}))(v).then(e=>{URL.revokeObjectURL(v);const n=((t,e,n={},i={})=>{const{canvasMemoryLimit:o,background:r=null}=i,c=n.zoom||1,g=d(t,e,n.flip),u={width:g.width,height:g.height},m=n.aspectRatio||u.height/u.width;let y=s(u,m,c);if(o){const t=y.width*y.height;if(t>o){const e=Math.sqrt(o)/Math.sqrt(t);u.width=Math.floor(u.width*e),u.height=Math.floor(u.height*e),y=s(u,m,c)}}const p=document.createElement("canvas"),f={x:.5*y.width,y:.5*y.height},w={x:0,y:0,width:y.width,height:y.height,center:f},x=void 0===n.scaleToFit||n.scaleToFit,M=c*a(u,l(w,m),n.rotation,x?n.center:{x:.5,y:.5});p.width=Math.round(y.width/M),p.height=Math.round(y.height/M),f.x/=M,f.y/=M;const T=f.x-u.width*(n.center?n.center.x:.5),A=f.y-u.height*(n.center?n.center.y:.5),b=p.getContext("2d");r&&(b.fillStyle=r,b.fillRect(0,0,p.width,p.height)),b.translate(f.x,f.y),b.rotate(n.rotation||0),b.drawImage(g,T-f.x,A-f.y,u.width,u.height);const R=b.getImageData(0,0,p.width,p.height);return h(p),R})(e,x,m,{canvasMemoryLimit:u,background:b}),i={quality:T,type:A||t.type};if(!R.length)return _(n,i);const o=P(L);o.post({transforms:R,imageData:n},t=>{_((t=>{let e;try{e=new ImageData(t.width,t.height)}catch(n){e=document.createElement("canvas").getContext("2d").createImageData(t.width,t.height)}return e.data.set(t.data),e})(t),i),o.terminate()},[n.data.buffer])}).catch(o)}),z=["x","y","left","top","right","bottom","width","height"],D=t=>{const[e,n]=t,i=n.points?{}:z.reduce((t,e)=>(t[e]=(t=>"string"==typeof t&&/%/.test(t)?parseFloat(t)/100:t)(n[e]),t),{});return[e,{zIndex:0,...n,...i}]};"undefined"!=typeof window&&void 0!==window.document&&(HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,"toBlob",{value:function(t,e,n){const i=this;setTimeout(()=>{const o=i.toDataURL(e,n).split(",")[1],r=atob(o);let a=r.length;const l=new Uint8Array(a);for(;a--;)l[a]=r.charCodeAt(a);t(new Blob([l],{type:e||"image/png"}))})}}));const j="undefined"!=typeof window&&void 0!==window.document,q=j&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,H=({addFilter:e,utils:n})=>{const{Type:i,forin:o,getFileFromBlob:r,isFile:a}=n,l=["crop","resize","filter","markup","output"],s=t=>null===t.aspectRatio&&0===t.rotation&&1===t.zoom&&t.center&&.5===t.center.x&&.5===t.center.y&&t.flip&&!1===t.flip.horizontal&&!1===t.flip.vertical;e("SHOULD_PREPARE_OUTPUT",(t,{query:e})=>new Promise(t=>{t(!e("IS_ASYNC"))}));const h=(t,e,n)=>new Promise(i=>{if(!t("GET_ALLOW_IMAGE_TRANSFORM")||n.archived||!a(e)||!(t=>/^image/.test(t.type))(e))return i(!1);(t=>new Promise((e,n)=>{const i=new Image;i.src=URL.createObjectURL(t);const o=()=>{const t=i.naturalWidth,n=i.naturalHeight;t&&n&&(URL.revokeObjectURL(i.src),clearInterval(r),e({width:t,height:n}))};i.onerror=(t=>{URL.revokeObjectURL(i.src),clearInterval(r),n(t)});const r=setInterval(o,1);o()}))(e).then(()=>{const n=t("GET_IMAGE_TRANSFORM_IMAGE_FILTER");if(n){const t=n(e);if(null==t)return handleRevert(!0);if("boolean"==typeof t)return i(t);if("function"==typeof t.then)return t.then(i)}i(!0)}).catch(t=>{i(!1)})});return e("DID_CREATE_ITEM",(t,{query:e,dispatch:n})=>{e("GET_ALLOW_IMAGE_TRANSFORM")&&t.extend("requestPrepare",()=>new Promise((e,i)=>{n("REQUEST_PREPARE_OUTPUT",{query:t.id,item:t,success:e,failure:i},!0)}))}),e("PREPARE_OUTPUT",(e,{query:n,item:i})=>new Promise(a=>{h(n,e,i).then(h=>{if(!h)return a(e);const c=[];n("GET_IMAGE_TRANSFORM_VARIANTS_INCLUDE_ORIGINAL")&&c.push(()=>new Promise(t=>{t({name:n("GET_IMAGE_TRANSFORM_VARIANTS_ORIGINAL_NAME"),file:e})})),n("GET_IMAGE_TRANSFORM_VARIANTS_INCLUDE_DEFAULT")&&c.push((t,e,i)=>new Promise(o=>{t(e,i).then(t=>o({name:n("GET_IMAGE_TRANSFORM_VARIANTS_DEFAULT_NAME"),file:t}))}));const d=n("GET_IMAGE_TRANSFORM_VARIANTS")||{};o(d,(t,e)=>{const n=(t=>(e,n,i)=>e(n,t?t(i):i))(e);c.push((e,i,o)=>new Promise(r=>{n(e,i,o).then(e=>r({name:t,file:e}))}))});const g=n("GET_IMAGE_TRANSFORM_OUTPUT_QUALITY"),u=n("GET_IMAGE_TRANSFORM_OUTPUT_QUALITY_MODE"),m=null===g?null:g/100,y=n("GET_IMAGE_TRANSFORM_OUTPUT_MIME_TYPE"),p=n("GET_IMAGE_TRANSFORM_CLIENT_TRANSFORMS")||l;i.setMetadata("output",{type:y,quality:m,client:p},!0);const f=(e,i)=>new Promise((o,a)=>{const l={...i};Object.keys(l).filter(t=>"exif"!==t).forEach(t=>{-1===p.indexOf(t)&&delete l[t]});const{resize:h,exif:c,output:d,crop:g,filter:m,markup:y}=l,f={image:{orientation:c?c.orientation:null},output:d&&(d.type||"number"==typeof d.quality||d.background)?{type:d.type,quality:"number"==typeof d.quality?100*d.quality:null,background:d.background||n("GET_IMAGE_TRANSFORM_CANVAS_BACKGROUND_COLOR")||null}:void 0,size:h&&(h.size.width||h.size.height)?{mode:h.mode,upscale:h.upscale,...h.size}:void 0,crop:g&&!s(g)?{...g}:void 0,markup:y&&y.length?y.map(D):[],filter:m};if(f.output){const t=!!d.type&&d.type!==e.type,n=/\/jpe?g$/.test(e.type),i=null!==d.quality&&(n&&"always"===u);if(!!!(f.size||f.crop||f.filter||t||i))return o(e)}const w={beforeCreateBlob:n("GET_IMAGE_TRANSFORM_BEFORE_CREATE_BLOB"),afterCreateBlob:n("GET_IMAGE_TRANSFORM_AFTER_CREATE_BLOB"),canvasMemoryLimit:n("GET_IMAGE_TRANSFORM_CANVAS_MEMORY_LIMIT"),stripImageHead:n("GET_IMAGE_TRANSFORM_OUTPUT_STRIP_IMAGE_HEAD")};G(e,f,w).then(n=>{const i=r(n,((e,n)=>{const i=(t=>t.substr(0,t.lastIndexOf("."))||t)(e),o=n.split("/")[1];return`${i}.${t[o]||o}`})(e.name,(t=>/jpeg|png|svg\+xml/.test(t)?t:"image/jpeg")(n.type)));o(i)}).catch(a)}),w=c.map(t=>t(f,e,i.getMetadata()));Promise.all(w).then(t=>{a(1===t.length&&null===t[0].name?t[0].file:t)})})})),{options:{allowImageTransform:[!0,i.BOOLEAN],imageTransformImageFilter:[null,i.FUNCTION],imageTransformOutputMimeType:[null,i.STRING],imageTransformOutputQuality:[null,i.INT],imageTransformOutputStripImageHead:[!0,i.BOOLEAN],imageTransformClientTransforms:[null,i.ARRAY],imageTransformOutputQualityMode:["always",i.STRING],imageTransformVariants:[null,i.OBJECT],imageTransformVariantsIncludeDefault:[!0,i.BOOLEAN],imageTransformVariantsDefaultName:[null,i.STRING],imageTransformVariantsIncludeOriginal:[!1,i.BOOLEAN],imageTransformVariantsOriginalName:["original_",i.STRING],imageTransformBeforeCreateBlob:[null,i.FUNCTION],imageTransformAfterCreateBlob:[null,i.FUNCTION],imageTransformCanvasMemoryLimit:[j&&q?16777216:null,i.INT],imageTransformCanvasBackgroundColor:[null,i.STRING]}}};j&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:H}));export default H;
