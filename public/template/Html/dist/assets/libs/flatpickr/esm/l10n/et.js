var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Estonian = {
    weekdays: {
        shorthand: ["<PERSON>", "E", "T", "<PERSON>", "N", "R", "L"],
        longhand: [
            "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
        ],
    },
    months: {
        shorthand: [
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "Apr",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "Aug",
            "Sept",
            "Okt",
            "Nov",
            "Dets",
        ],
        longhand: [
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "Aprill",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "August",
            "September",
            "Oktoober",
            "November",
            "Detsember",
        ],
    },
    firstDayOfWeek: 1,
    ordinal: function () {
        return ".";
    },
    weekAbbreviation: "Näd",
    rangeSeparator: " kuni ",
    scrollTitle: "Keri, et suurendada",
    toggleTitle: "Klõpsa, et vahetada",
    time_24hr: true,
};
fp.l10ns.et = Estonian;
export default fp.l10ns;
