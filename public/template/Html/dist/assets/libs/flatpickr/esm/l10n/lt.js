var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Lithuanian = {
    weekdays: {
        shorthand: ["S", "Pr", "A", "T", "K", "Pn", "Š"],
        longhand: [
            "<PERSON>k<PERSON><PERSON><PERSON>",
            "Pirmadienis",
            "Antradienis",
            "Trečiadienis",
            "Ketvirtadienis",
            "Penktadienis",
            "Šeštadienis",
        ],
    },
    months: {
        shorthand: [
            "Sau",
            "Vas",
            "Kov",
            "Bal",
            "Geg",
            "Bir",
            "Lie",
            "Rgp",
            "Rgs",
            "Spl",
            "Lap",
            "Grd",
        ],
        longhand: [
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>land<PERSON>",
            "<PERSON>eg<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "Rugpjūtis",
            "Rugsėjis",
            "<PERSON>lis",
            "Lapkritis",
            "G<PERSON>odis",
        ],
    },
    firstDayOfWeek: 1,
    ordinal: function () {
        return "-a";
    },
    rangeSeparator: " iki ",
    weekAbbreviation: "Sav",
    scrollTitle: "Keisti laiką pelės rateliu",
    toggleTitle: "Perjungti laiko formatą",
    time_24hr: true,
};
fp.l10ns.lt = Lithuanian;
export default fp.l10ns;
