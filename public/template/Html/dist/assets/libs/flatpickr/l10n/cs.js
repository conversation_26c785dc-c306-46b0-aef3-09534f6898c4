(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.cs = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Czech = {
      weekdays: {
          shorthand: ["Ne", "Po", "Út", "<PERSON>", "<PERSON>t", "P<PERSON>", "So"],
          longhand: [
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>í",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "Čtvrtek",
              "Pátek",
              "Sobota",
          ],
      },
      months: {
          shorthand: [
              "Led",
              "Ún",
              "Bře",
              "<PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>v<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "Říj",
              "<PERSON><PERSON>",
              "<PERSON>",
          ],
          longhand: [
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "Červen",
              "Červenec",
              "Sr<PERSON>",
              "Z<PERSON>ří",
              "Říjen",
              "Listopad",
              "Prosinec",
          ],
      },
      firstDayOfWeek: 1,
      ordinal: function () {
          return ".";
      },
      rangeSeparator: " do ",
      weekAbbreviation: "Týd.",
      scrollTitle: "Rolujte pro změnu",
      toggleTitle: "Přepnout dopoledne/odpoledne",
      amPM: ["dop.", "odp."],
      yearAriaLabel: "Rok",
      time_24hr: true,
  };
  fp.l10ns.cs = Czech;
  var cs = fp.l10ns;

  exports.Czech = Czech;
  exports.default = cs;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
