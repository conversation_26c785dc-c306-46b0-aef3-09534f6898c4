(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.ga = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Irish = {
      firstDayOfWeek: 1,
      weekdays: {
          shorthand: ["Dom", "<PERSON>a", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sat"],
          longhand: [
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>é <PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>éardao<PERSON>",
              "<PERSON>é hAoine",
              "<PERSON><PERSON> Sathairn",
          ],
      },
      months: {
          shorthand: [
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON>",
              "<PERSON><PERSON>",
          ],
          longhand: [
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON>breán",
              "Bealtaine",
              "Meitheamh",
              "Iúil",
              "Lúnasa",
              "Meán Fómhair",
              "Deireadh Fómhair",
              "Samhain",
              "Nollaig",
          ],
      },
      time_24hr: true,
  };
  fp.l10ns.hr = Irish;
  var ga = fp.l10ns;

  exports.Irish = Irish;
  exports.default = ga;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
