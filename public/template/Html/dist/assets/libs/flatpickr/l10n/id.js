(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.id = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Indonesian = {
      weekdays: {
          shorthand: ["Min", "<PERSON>", "<PERSON><PERSON>", "Rab", "<PERSON>m", "Ju<PERSON>", "Sab"],
          longhand: ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ka<PERSON>", "Jumat", "Sabtu"],
      },
      months: {
          shorthand: [
              "Jan",
              "Feb",
              "Mar",
              "Apr",
              "Mei",
              "Jun",
              "Jul",
              "<PERSON>gu",
              "Sep",
              "Okt",
              "Nov",
              "<PERSON>",
          ],
          longhand: [
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "April",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>gus<PERSON>",
              "September",
              "<PERSON><PERSON><PERSON>",
              "November",
              "Des<PERSON><PERSON>",
          ],
      },
      firstDayOfWeek: 1,
      ordinal: function () {
          return "";
      },
      time_24hr: true,
      rangeSeparator: " - ",
  };
  fp.l10ns.id = Indonesian;
  var id = fp.l10ns;

  exports.Indonesian = Indonesian;
  exports.default = id;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
