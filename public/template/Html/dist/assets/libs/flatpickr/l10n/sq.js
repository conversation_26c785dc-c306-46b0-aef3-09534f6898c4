(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.sq = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Albanian = {
      weekdays: {
          shorthand: ["<PERSON>", "<PERSON>ë", "Ma", "<PERSON>ë", "En", "Pr", "Sh"],
          longhand: [
              "<PERSON> Diel",
              "<PERSON> Hënë",
              "<PERSON> Martë",
              "<PERSON> Mërkurë",
              "<PERSON> Enj<PERSON>",
              "E Premte",
              "E Shtunë",
          ],
      },
      months: {
          shorthand: [
              "Jan",
              "Shk",
              "Mar",
              "<PERSON><PERSON>",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "Dhj",
          ],
          longhand: [
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "Tetor",
              "Nëntor",
              "Dhjetor",
          ],
      },
      firstDayOfWeek: 1,
      rangeSeparator: " deri ",
      weekAbbreviation: "Java",
      yearAriaLabel: "Viti",
      monthAriaLabel: "Muaji",
      hourAriaLabel: "Ora",
      minuteAriaLabel: "Minuta",
      time_24hr: true,
  };
  fp.l10ns.sq = Albanian;
  var sq = fp.l10ns;

  exports.Albanian = Albanian;
  exports.default = sq;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
